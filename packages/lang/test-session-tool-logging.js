#!/usr/bin/env node

/**
 * 测试SessionManager是否正确记录工具调用和工具结果到.haicode目录
 */

import { createLangChainGeminiCLI } from './dist/index.js';
import { promises as fs } from 'node:fs';
import path from 'node:path';

async function testSessionToolLogging() {
  console.log('🧪 测试SessionManager工具调用记录功能...\n');

  try {
    // 创建LangChain CLI实例
    const cli = await createLangChainGeminiCLI({
      model: 'gemini-1.5-flash',
      apiKey: process.env.GEMINI_API_KEY,
      targetDir: process.cwd(),
      debugMode: true
    });

    console.log('✅ LangChain CLI实例创建成功');
    console.log(`🔧 工具数量: ${cli.config.getTools().length}`);
    console.log(`📋 可用工具: ${cli.config.getTools().map(t => t.name).join(', ')}\n`);

    // 创建一个新会话
    const sessionId = cli.sessionManager.createSession('测试工具调用记录');
    console.log(`📝 创建会话: ${sessionId}\n`);

    // 测试工具调用 - 请求读取文件
    console.log('🔍 测试工具调用 - 请求读取package.json:');
    const response = await cli.processMessage(
      '请读取package.json文件的内容，并告诉我项目名称',
      sessionId
    );
    
    console.log('\n📤 AI响应:');
    console.log(response);
    console.log('\n');

    // 等待一段时间确保日志写入完成
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 检查会话历史
    const history = cli.sessionManager.getConversationHistory(sessionId);
    console.log(`📚 会话历史消息数量: ${history.length}`);
    
    // 打印消息类型统计
    const messageTypes = {};
    history.forEach(msg => {
      const type = msg.constructor.name;
      messageTypes[type] = (messageTypes[type] || 0) + 1;
    });
    
    console.log('📊 消息类型统计:');
    Object.entries(messageTypes).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count}`);
    });

    // 检查是否有工具调用消息
    const aiMessagesWithTools = history.filter(msg => 
      msg.constructor.name === 'AIMessage' && msg.tool_calls && msg.tool_calls.length > 0
    );
    const toolMessages = history.filter(msg => msg.constructor.name === 'ToolMessage');
    
    console.log(`🔧 包含工具调用的AI消息: ${aiMessagesWithTools.length}`);
    console.log(`🛠️ 工具结果消息: ${toolMessages.length}`);

    if (aiMessagesWithTools.length > 0) {
      console.log('\n🔍 工具调用详情:');
      aiMessagesWithTools.forEach((msg, index) => {
        console.log(`  消息 ${index + 1}:`);
        msg.tool_calls.forEach(tc => {
          console.log(`    - 工具: ${tc.name}`);
          console.log(`    - 参数: ${JSON.stringify(tc.args, null, 2)}`);
        });
      });
    }

    if (toolMessages.length > 0) {
      console.log('\n🛠️ 工具结果详情:');
      toolMessages.forEach((msg, index) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        const preview = content.length > 100 ? content.substring(0, 100) + '...' : content;
        console.log(`  结果 ${index + 1}: ${preview}`);
      });
    }

    // 检查.haicode目录中的日志文件
    const sessionLogger = cli.sessionManager.getSessionLogger(sessionId);
    if (sessionLogger) {
      const logs = sessionLogger.getSessionLogs();
      console.log(`\n📝 .haicode日志条目数量: ${logs.length}`);
      
      // 统计日志类型
      const logTypes = {};
      logs.forEach(log => {
        const key = log.metadata?.tool_calls ? 'AI_WITH_TOOLS' : 
                   log.metadata?.message_type === 'tool_result' ? 'TOOL_RESULT' :
                   log.type;
        logTypes[key] = (logTypes[key] || 0) + 1;
      });
      
      console.log('📊 日志类型统计:');
      Object.entries(logTypes).forEach(([type, count]) => {
        console.log(`  - ${type}: ${count}`);
      });

      // 显示包含工具调用的日志条目
      const toolCallLogs = logs.filter(log => log.metadata?.tool_calls);
      const toolResultLogs = logs.filter(log => log.metadata?.message_type === 'tool_result');
      
      if (toolCallLogs.length > 0) {
        console.log('\n🔧 工具调用日志:');
        toolCallLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log.message}`);
        });
      }

      if (toolResultLogs.length > 0) {
        console.log('\n🛠️ 工具结果日志:');
        toolResultLogs.forEach((log, index) => {
          const preview = log.message.length > 100 ? log.message.substring(0, 100) + '...' : log.message;
          console.log(`  ${index + 1}. ${preview}`);
        });
      }
    }

    // 验证结果
    const hasToolCalls = aiMessagesWithTools.length > 0;
    const hasToolResults = toolMessages.length > 0;
    const hasToolCallLogs = sessionLogger && sessionLogger.getSessionLogs().some(log => log.metadata?.tool_calls);
    const hasToolResultLogs = sessionLogger && sessionLogger.getSessionLogs().some(log => log.metadata?.message_type === 'tool_result');

    console.log('\n🎯 测试结果:');
    console.log(`✅ 会话中记录了工具调用: ${hasToolCalls ? '是' : '否'}`);
    console.log(`✅ 会话中记录了工具结果: ${hasToolResults ? '是' : '否'}`);
    console.log(`✅ .haicode中记录了工具调用: ${hasToolCallLogs ? '是' : '否'}`);
    console.log(`✅ .haicode中记录了工具结果: ${hasToolResultLogs ? '是' : '否'}`);

    if (hasToolCalls && hasToolResults && hasToolCallLogs && hasToolResultLogs) {
      console.log('\n🎉 测试通过！SessionManager正确记录了工具调用和结果。');
    } else {
      console.log('\n❌ 测试失败！SessionManager未能正确记录工具调用或结果。');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('堆栈:', error.stack);
    }
  }
}

// 运行测试
testSessionToolLogging();
