#!/usr/bin/env node

/**
 * 简单测试SessionManager的日志记录功能，不依赖LLM调用
 */

import { SessionManager } from './dist/core/sessionManager.js';
import { HumanMessage, AIMessage, ToolMessage } from '@langchain/core/messages';

async function testSessionLogging() {
  console.log('🧪 测试SessionManager日志记录功能...\n');

  try {
    // 创建SessionManager实例
    const sessionManager = new SessionManager(
      100, // maxSessions
      24 * 60 * 60 * 1000, // sessionTimeout (24 hours)
      true, // persistenceEnabled
      process.cwd() // targetDir
    );

    console.log('✅ SessionManager创建成功');

    // 打印.haicode目录路径
    const { getProjectTempDir } = await import('./dist/utils/paths.js');
    const haicodeDir = getProjectTempDir(process.cwd());
    console.log(`📁 .haicode目录: ${haicodeDir}`);

    // 创建一个新会话
    const sessionId = sessionManager.createSession('测试工具调用记录');
    console.log(`📝 创建会话: ${sessionId}\n`);

    // 等待logger初始化
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟一个完整的工具调用对话
    console.log('📝 添加用户消息...');
    const userMessage = new HumanMessage('请读取package.json文件的内容');
    const result1 = sessionManager.addMessage(sessionId, userMessage);
    console.log(`   结果: ${result1}`);

    console.log('📝 添加AI工具调用消息...');
    const aiMessageWithTools = new AIMessage({
      content: '我来帮你读取package.json文件的内容。',
      tool_calls: [
        {
          id: 'call_123',
          name: 'read_file',
          args: { absolute_path: '/Users/<USER>/projs/github/gemini-cli/packages/lang/package.json' }
        }
      ]
    });
    console.log(`   消息类型: ${aiMessageWithTools.constructor.name}`);
    console.log(`   有工具调用: ${!!(aiMessageWithTools.tool_calls && aiMessageWithTools.tool_calls.length > 0)}`);
    const result2 = sessionManager.addMessage(sessionId, aiMessageWithTools);
    console.log(`   结果: ${result2}`);
    console.log(`   工具调用: ${JSON.stringify(aiMessageWithTools.tool_calls)}`);

    console.log('📝 添加工具结果消息...');
    const toolMessage = new ToolMessage({
      content: JSON.stringify({
        name: "@ht/hai-code-cli",
        version: "0.1.0",
        description: "LangChain.js and LangGraph based implementation of Coding agent"
      }, null, 2),
      tool_call_id: 'call_123'
    });
    console.log(`   消息类型: ${toolMessage.constructor.name}`);
    const result3 = sessionManager.addMessage(sessionId, toolMessage);
    console.log(`   结果: ${result3}`);
    console.log(`   工具调用ID: ${toolMessage.tool_call_id}`);

    console.log('📝 添加最终AI响应...');
    const finalAiMessage = new AIMessage('根据package.json文件，这个项目的名称是 "@ht/hai-code-cli"，版本是 "0.1.0"。');
    const result4 = sessionManager.addMessage(sessionId, finalAiMessage);
    console.log(`   结果: ${result4}`);

    // 等待日志写入完成
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 强制刷新日志
    let sessionLogger = sessionManager.getSessionLogger(sessionId);
    if (sessionLogger) {
      console.log('📝 强制刷新日志...');
      // 等待所有异步日志操作完成
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 检查会话历史
    const history = sessionManager.getConversationHistory(sessionId);
    console.log(`\n📚 会话历史消息数量: ${history.length}`);
    
    // 打印消息类型统计
    const messageTypes = {};
    history.forEach(msg => {
      const type = msg.constructor.name;
      messageTypes[type] = (messageTypes[type] || 0) + 1;
    });
    
    console.log('📊 消息类型统计:');
    Object.entries(messageTypes).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count}`);
    });

    // 检查是否有工具调用消息
    const aiMessagesWithTools = history.filter(msg => 
      msg.constructor.name === 'AIMessage' && msg.tool_calls && msg.tool_calls.length > 0
    );
    const toolMessages = history.filter(msg => msg.constructor.name === 'ToolMessage');
    
    console.log(`🔧 包含工具调用的AI消息: ${aiMessagesWithTools.length}`);
    console.log(`🛠️ 工具结果消息: ${toolMessages.length}`);

    if (aiMessagesWithTools.length > 0) {
      console.log('\n🔍 工具调用详情:');
      aiMessagesWithTools.forEach((msg, index) => {
        console.log(`  消息 ${index + 1}:`);
        msg.tool_calls.forEach(tc => {
          console.log(`    - 工具: ${tc.name}`);
          console.log(`    - 参数: ${JSON.stringify(tc.args, null, 2)}`);
        });
      });
    }

    if (toolMessages.length > 0) {
      console.log('\n🛠️ 工具结果详情:');
      toolMessages.forEach((msg, index) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        const preview = content.length > 100 ? content.substring(0, 100) + '...' : content;
        console.log(`  结果 ${index + 1}: ${preview}`);
      });
    }

    // 检查.haicode目录中的日志文件
    sessionLogger = sessionManager.getSessionLogger(sessionId);
    if (sessionLogger) {
      const allLogs = sessionLogger.getSessionLogs();
      // 只显示当前会话的日志
      const logs = allLogs.filter(log => log.sessionId === sessionId);
      console.log(`\n📝 .haicode当前会话日志条目数量: ${logs.length}`);
      console.log(`📝 .haicode总日志条目数量: ${allLogs.length}`);

      // 统计当前会话的日志类型
      const logTypes = {};
      logs.forEach(log => {
        const key = log.metadata?.tool_calls ? 'AI_WITH_TOOLS' :
                   log.metadata?.message_type === 'tool_result' ? 'TOOL_RESULT' :
                   log.type;
        logTypes[key] = (logTypes[key] || 0) + 1;
      });

      console.log('📊 当前会话日志类型统计:');
      Object.entries(logTypes).forEach(([type, count]) => {
        console.log(`  - ${type}: ${count}`);
      });

      // 显示包含工具调用的日志条目
      const toolCallLogs = logs.filter(log => log.metadata?.tool_calls);
      const toolResultLogs = logs.filter(log => log.metadata?.message_type === 'tool_result');

      if (toolCallLogs.length > 0) {
        console.log('\n🔧 工具调用日志:');
        toolCallLogs.forEach((log, index) => {
          console.log(`  ${index + 1}. ${log.message}`);
          if (log.metadata?.tool_calls) {
            log.metadata.tool_calls.forEach(tc => {
              console.log(`     工具: ${tc.name}, 参数: ${JSON.stringify(tc.args)}`);
            });
          }
        });
      }

      if (toolResultLogs.length > 0) {
        console.log('\n🛠️ 工具结果日志:');
        toolResultLogs.forEach((log, index) => {
          const preview = log.message.length > 100 ? log.message.substring(0, 100) + '...' : log.message;
          console.log(`  ${index + 1}. ${preview}`);
        });
      }
    }

    // 验证结果
    const hasToolCalls = aiMessagesWithTools.length > 0;
    const hasToolResults = toolMessages.length > 0;
    const sessionLogs = sessionLogger ? sessionLogger.getSessionLogs().filter(log => log.sessionId === sessionId) : [];
    const hasToolCallLogs = sessionLogs.some(log => log.metadata?.tool_calls);
    const hasToolResultLogs = sessionLogs.some(log => log.metadata?.message_type === 'tool_result');

    console.log('\n🎯 测试结果:');
    console.log(`✅ 会话中记录了工具调用: ${hasToolCalls ? '是' : '否'}`);
    console.log(`✅ 会话中记录了工具结果: ${hasToolResults ? '是' : '否'}`);
    console.log(`✅ .haicode中记录了工具调用: ${hasToolCallLogs ? '是' : '否'}`);
    console.log(`✅ .haicode中记录了工具结果: ${hasToolResultLogs ? '是' : '否'}`);

    if (hasToolCalls && hasToolResults && hasToolCallLogs && hasToolResultLogs) {
      console.log('\n🎉 测试通过！SessionManager正确记录了工具调用和结果。');
      return true;
    } else {
      console.log('\n❌ 测试失败！SessionManager未能正确记录工具调用或结果。');
      return false;
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('堆栈:', error.stack);
    }
    return false;
  }
}

// 运行测试
testSessionLogging().then(success => {
  process.exit(success ? 0 : 1);
});
