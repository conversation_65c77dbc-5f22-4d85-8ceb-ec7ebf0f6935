/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Tool } from '@langchain/core/tools';
import {
  AIMessage,
  AIMessageChunk,
  HumanMessage,
  SystemMessage,
  BaseMessage,
  ToolMessage,
  isAIMessageChunk,
} from '@langchain/core/messages';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import logger from '../utils/logger.js';

// Import LangGraph components
import { StateGraph, Annotation } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { MemorySaver } from '@langchain/langgraph';
import type { BaseCheckpointSaver } from '@langchain/langgraph';

import type { LangChainConfig } from '../config/config.js';

const MAX_TOOL_RESULT_LOG_LENGTH = 500;

/**
 * Simplified state definition for the LangGraph agent
 * Following official LangGraph.js best practices
 */
const AgentState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (current: BaseMessage[], update: BaseMessage[]) => current.concat(update),
    default: () => [],
  }),
  userMemory: Annotation<string>({
    reducer: (current: string, update: string) => update || current,
    default: () => "",
  }),
  iterationCount: Annotation<number>({
    reducer: (_current: number, update: number) => update,
    default: () => 0,
  }),
});

type AgentStateType = typeof AgentState.State;
type AgentUpdateType = typeof AgentState.Update;
type CompiledGraphType = ReturnType<StateGraph<typeof AgentState>['compile']>;

// Type-safe node names
const NODE_NAMES = {
  AGENT: "agent",
  TOOLS: "tools",
} as const;

/**
 * Simplified LangGraph-based agent using StateGraph
 * Following official LangGraph.js best practices
 */
export class StateGraphAgent {
  private chatModel: BaseChatModel;
  private systemPrompt: string;
  private config: LangChainConfig;
  private checkpointer?: BaseCheckpointSaver;
  private graph: CompiledGraphType;
  private maxIterations: number = 25;

  constructor(
    config: LangChainConfig,
    systemPrompt?: string,
    options: {
      checkpointer?: BaseCheckpointSaver;
      enablePersistence?: boolean;
      maxIterations?: number;
    } = {}
  ) {
    this.config = config;
    this.chatModel = config.chatModel;
    this.systemPrompt = systemPrompt || this.getCoreSystemPrompt();
    this.maxIterations = options.maxIterations || 25;

    // Set up checkpointer for persistence
    if (options.enablePersistence !== false) {
      this.checkpointer = options.checkpointer || new MemorySaver();
    }

    this.graph = this.buildGraph();
  }

  /**
   * Get the current tools from config (always up-to-date)
   */
  private getTools(): Tool[] {
    return this.config.getTools();
  }

  /**
   * Create a debug wrapper for the tool node
   * Simplified version with proper type handling
   */
  private createDebugToolWrapper(toolNode: ToolNode<Tool[]>) {
    return async (state: AgentStateType): Promise<AgentUpdateType> => {
      const messages = state.messages;
      const lastMessage = messages[messages.length - 1];

      // Extract tool calls from the last AI message
      if ((lastMessage instanceof AIMessage || lastMessage instanceof AIMessageChunk) && lastMessage.tool_calls?.length) {
        if (this.config.getDebugMode()) {
          logger.debug(`[工具调用] 🔧 StateGraph执行工具: ${lastMessage.tool_calls.map(tc => tc.name).join(', ')}`);
          for (const toolCall of lastMessage.tool_calls) {
            logger.debug(`[工具调用] 📥 工具 ${toolCall.name} 输入参数: ${JSON.stringify(toolCall.args, null, 2)}`);
          }
        }
      }

      // Execute the original tool node - suppress type checking for LangGraph API compatibility
      // @ts-expect-error - ToolNode.invoke expects different state type but works at runtime
      const result = await toolNode.invoke(state);

      // Log tool results in debug mode
      if (this.config.getDebugMode() && result && 'messages' in result && Array.isArray(result.messages)) {
        for (const message of result.messages) {
          if (message instanceof ToolMessage) {
            const toolMessage = message;
            logger.debug(`[工具调用] ✅ 工具执行完成`);
            logger.debug(`[工具调用] 📤 输出结果: ${typeof toolMessage.content === 'string' ? toolMessage.content.substring(0, MAX_TOOL_RESULT_LOG_LENGTH) + (toolMessage.content.length > MAX_TOOL_RESULT_LOG_LENGTH ? '...(省略)' : '') : JSON.stringify(toolMessage.content).substring(0, MAX_TOOL_RESULT_LOG_LENGTH)}`);
          }
        }
      }

      return result as AgentUpdateType;
    };
  }

  /**
   * Build the StateGraph with nodes and edges
   * Simplified version following LangGraph.js best practices
   */
  private buildGraph(): CompiledGraphType {
    const workflow = new StateGraph(AgentState);

    // Add the agent node (calls the LLM)
    workflow.addNode(NODE_NAMES.AGENT, this.callModel.bind(this));

    // Add tool node using LangGraph's built-in ToolNode with debug wrapper
    const toolNode = new ToolNode(this.getTools());
    workflow.addNode(NODE_NAMES.TOOLS, this.createDebugToolWrapper(toolNode));

    // Set entry point using addEdge with __start__
    (workflow as any).addEdge("__start__", NODE_NAMES.AGENT);

    // Add conditional edges from agent
    (workflow as any).addConditionalEdges(
      NODE_NAMES.AGENT,
      this.shouldContinue.bind(this),
      {
        continue: NODE_NAMES.TOOLS,
        end: "__end__",
      }
    );

    // Add edge from tools back to agent
    (workflow as any).addEdge(NODE_NAMES.TOOLS, NODE_NAMES.AGENT);

    // Compile the graph with checkpointer for persistence
    const compileOptions: { checkpointer?: BaseCheckpointSaver } = {};
    if (this.checkpointer) {
      compileOptions.checkpointer = this.checkpointer;
    }

    // @ts-expect-error - Return type is complex but runtime-safe
    return workflow.compile(compileOptions);
  }

  /**
   * The agent node - calls the LLM with the current state
   * Simplified error handling following LangGraph.js best practices
   */
  private async callModel(state: AgentStateType): Promise<AgentUpdateType> {
    logger.debug('[StateGraphAgent] Calling model with', state.messages.length, 'messages');

    // Prepare messages with system prompt
    const messages = this.prepareMessages(state.messages, state.userMemory);

    // Bind tools to the model if available
    let modelWithTools: BaseChatModel = this.chatModel;
    const tools = this.getTools();
    if (tools.length > 0 && this.chatModel.bindTools) {
      try {
        // Temporarily suppress Zod schema warnings during tool binding
        const originalWarn = console.warn;
        console.warn = (message: string, ...args: unknown[]) => {
          if (typeof message === 'string' &&
              message.includes('Zod field') &&
              message.includes('uses `.optional()` without `.nullable()`')) {
            return; // Suppress Zod schema warnings
          }
          originalWarn(message, ...args);
        };

        modelWithTools = this.chatModel.bindTools(tools) as BaseChatModel;

        // Restore original console.warn
        console.warn = originalWarn;

        logger.debug('[StateGraphAgent] Tools successfully bound to model');
      } catch (error) {
        logger.warning('[StateGraphAgent] Tool binding failed, proceeding without tools:', error);
      }
    }

    try {
      // Generate response
      const response = await modelWithTools.invoke(messages);

      // Increment iteration count
      const newIterationCount = state.iterationCount + 1;

      // Check if we've exceeded max iterations
      if (newIterationCount >= this.maxIterations) {
        logger.warning(`[StateGraphAgent] Maximum iterations (${this.maxIterations}) reached`);

        // Force end by creating a non-tool response
        const finalResponse = new AIMessage({
          content: response instanceof AIMessage && typeof response.content === 'string'
            ? response.content
            : "I've reached the maximum number of iterations. Let me know if you need further assistance.",
        });

        return {
          messages: [finalResponse],
          iterationCount: newIterationCount,
        };
      }

      return {
        messages: [response],
        iterationCount: newIterationCount,
      };
    } catch (error) {
      logger.error('[StateGraphAgent] Error calling model:', error);

      // Simplified error handling - just throw the error to let LangGraph handle it
      throw error;
    }
  }

  /**
   * Determines whether to continue to tools or end
   * Simplified logic following LangGraph.js best practices
   */
  private shouldContinue(state: AgentStateType): "continue" | "end" {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];

    // Check iteration limit
    if (state.iterationCount >= this.maxIterations) {
      logger.debug('[StateGraphAgent] Max iterations reached, ending');
      return "end";
    }

    // If the last message has tool calls, continue to tools
    if ((lastMessage instanceof AIMessage || lastMessage instanceof AIMessageChunk) && lastMessage.tool_calls?.length) {
      logger.debug('[StateGraphAgent] Tool calls detected, continuing to tools');
      return "continue";
    }

    // Otherwise, end the conversation
    logger.debug('[StateGraphAgent] No tool calls, ending conversation');
    return "end";
  }

  /**
   * Prepare messages for the chat model, including system prompt
   * Simplified version following LangGraph.js best practices
   */
  private prepareMessages(messages: BaseMessage[], userMemory?: string): BaseMessage[] {
    const preparedMessages: BaseMessage[] = [];

    // Use the core system prompt that mirrors the core package functionality
    let prompt = this.getCoreSystemPrompt(userMemory);

    // Add tool information to system prompt if tools are available
    const tools = this.getTools();
    if (tools.length > 0) {
      prompt += `\n\n# Available Tools\n\nYou have access to the following tools:\n`;
      for (const tool of tools) {
        prompt += `\n## ${tool.name}\n`;
        prompt += `**Description**: ${tool.description}\n`;
      }
      prompt += `\nUse these tools when needed to help the user. Always call the appropriate tool to gather information before making changes or providing answers.`;
    }

    preparedMessages.push(new SystemMessage(prompt));

    // Add conversation messages
    preparedMessages.push(...messages);

    // Debug mode: print complete LLM input
    if (this.config.getDebugMode()) {
      logger.debug('\n=== LLM 完整输入 ===');
      preparedMessages.forEach((message, index) => {
        const messageType = message.constructor.name;
        const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
        logger.debug(`\n[${index}] ${messageType}:`);
        logger.debug(content);
        logger.debug('---');
      });
      logger.debug('=== LLM 输入结束 ===\n');
    }

    return preparedMessages;
  }

  /**
   * Process a user message using the StateGraph
   * Simplified version following LangGraph.js best practices
   */
  async processMessage(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): Promise<string> {
    logger.debug('[StateGraphAgent] Processing message:', userMessage);

    // Validate input parameters
    if (!userMessage?.trim()) {
      throw new Error('Invalid user message: must be a non-empty string');
    }

    if (!sessionId?.trim()) {
      throw new Error('Invalid session ID: must be a non-empty string');
    }

    // Create initial state
    const initialState = {
      messages: [...conversationHistory, new HumanMessage(userMessage)],
      userMemory: userMemory || "",
      iterationCount: 0,
    };

    // Run the graph with proper configuration including thread_id
    const config = {
      recursionLimit: this.maxIterations,
      configurable: {
        thread_id: sessionId, // This is required for MemorySaver
      },
    };

    const result = await this.graph.invoke(initialState, config);

    // Extract the final response
    const finalMessages = result.messages;
    if (!finalMessages || finalMessages.length === 0) {
      logger.warning('[StateGraphAgent] No messages in result');
      return 'No response generated';
    }

    const lastMessage = finalMessages[finalMessages.length - 1];

    if (lastMessage instanceof AIMessage || lastMessage instanceof AIMessageChunk) {
      if (typeof lastMessage.content === 'string') {
        return lastMessage.content;
      } else if (lastMessage.content && typeof lastMessage.content === 'object') {
        return JSON.stringify(lastMessage.content);
      } else {
        return 'Empty response from model';
      }
    }

    logger.warning('[StateGraphAgent] Last message is not an AIMessage or AIMessageChunk:', lastMessage?.constructor?.name);
    return 'No valid response generated';
  }

  async *messageStream(message: AIMessage | AIMessageChunk) {
    if (message.content) {
      if (typeof message.content === 'string' && message.content.trim()) {
        yield message.content;
      } else if (Array.isArray(message.content)) {
        // Handle content arrays
        for (const part of message.content) {
          if (typeof part === 'string' && (part as string).trim()) {
            yield part as string;
          } else if (part && typeof part === 'object' && 'text' in part) {
            const textPart = part as { text?: string };
            if (typeof textPart.text === 'string') {
              yield textPart.text;
            }
          }
        }
      }
    }
  }

  /**
   * Stream responses using LangGraph.js native streaming
   * Following official documentation best practices
   */
  async *streamMessage(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): AsyncGenerator<string> {
    logger.debug('[StateGraphAgent] Streaming message:', userMessage);

    // Validate input parameters
    if (!userMessage?.trim()) {
      throw new Error('Invalid user message: must be a non-empty string');
    }

    if (!sessionId?.trim()) {
      throw new Error('Invalid session ID: must be a non-empty string');
    }

    try {
      // Create initial state
      const initialState = {
        messages: [...conversationHistory, new HumanMessage(userMessage)],
        userMemory: userMemory || "",
        iterationCount: 0,
      };

      // Configuration for streaming
      const config = {
        recursionLimit: this.maxIterations,
        configurable: {
          thread_id: sessionId,
        },
      };

      // Use LangGraph's native streaming with "messages" mode
      const stream = await this.graph.stream(initialState, { ...config, streamMode: "messages" });

      for await (const [message, _metadata] of stream) {
        if (isAIMessageChunk(message)) {
          // Handle tool call chunks
          if (message.tool_call_chunks?.length) {
            // Don't yield tool call chunks directly, let them complete
            continue;
          }

          // Yield content chunks - handle both string and empty content
          yield* this.messageStream(message);
        } else if (message instanceof AIMessage) {
          // Handle complete AI messages
          yield* this.messageStream(message);
        }
        // Tool messages are handled internally by the graph
      }

    } catch (error) {
      logger.error('[StateGraphAgent] Error streaming message:', error);
      yield `Error: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Alternative streaming method using streamEvents
   * For more granular control over streaming events
   */
  async *streamEvents(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): AsyncGenerator<string> {
    logger.debug('[StateGraphAgent] Streaming events for message:', userMessage);

    // Validate input parameters
    if (!userMessage?.trim()) {
      throw new Error('Invalid user message: must be a non-empty string');
    }

    if (!sessionId?.trim()) {
      throw new Error('Invalid session ID: must be a non-empty string');
    }

    try {
      // Create initial state
      const initialState = {
        messages: [...conversationHistory, new HumanMessage(userMessage)],
        userMemory: userMemory || "",
        iterationCount: 0,
      };

      // Configuration for streaming events
      const config = {
        recursionLimit: this.maxIterations,
        configurable: {
          thread_id: sessionId,
        },
        version: "v2" as const,
      };

      // Use LangGraph's streamEvents method
      const eventStream = this.graph.streamEvents(initialState, config);

      for await (const { event, data } of eventStream) {
        if (event === "on_chat_model_stream" && isAIMessageChunk(data.chunk)) {
          // Handle streaming LLM tokens
          if (data.chunk.content && typeof data.chunk.content === 'string') {
            yield data.chunk.content;
          }
        }
      }

    } catch (error) {
      logger.error('[StateGraphAgent] Error streaming events:', error);
      yield `Error: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Get the core system prompt that mirrors packages/core/src/core/prompts.ts
   */
  private getCoreSystemPrompt(userMemory?: string): string {
    const basePrompt = `
You are an interactive CLI agent specializing in software engineering tasks. Your primary goal is to help users safely and efficiently, adhering strictly to the following instructions and utilizing your available tools.

# Core Mandates

- **Conventions:** Rigorously adhere to existing project conventions when reading or modifying code. Analyze surrounding code, tests, and configuration first.
- **Libraries/Frameworks:** NEVER assume a library/framework is available or appropriate. Verify its established usage within the project (check imports, configuration files like 'package.json', 'Cargo.toml', 'requirements.txt', 'build.gradle', etc., or observe neighboring files) before employing it.
- **Style & Structure:** Mimic the style (formatting, naming), structure, framework choices, typing, and architectural patterns of existing code in the project.
- **Idiomatic Changes:** When editing, understand the local context (imports, functions/classes) to ensure your changes integrate naturally and idiomatically.
- **Comments:** Add code comments sparingly. Focus on *why* something is done, especially for complex logic, rather than *what* is done. Only add high-value comments if necessary for clarity or if requested by the user. Do not edit comments that are separate from the code you are changing. *NEVER* talk to the user or describe your changes through comments.
- **Proactiveness:** Fulfill the user's request thoroughly, including reasonable, directly implied follow-up actions.
- **Confirm Ambiguity/Expansion:** Do not take significant actions beyond the clear scope of the request without confirming with the user. If asked *how* to do something, explain first, don't just do it.
- **Explaining Changes:** After completing a code modification or file operation *do not* provide summaries unless asked.
- **Path Construction:** Before using any file system tool (e.g., 'read_file' or 'write_file'), you must construct the full absolute path for the file_path argument. Always combine the absolute path of the project's root directory with the file's path relative to the root. For example, if the project root is /path/to/project/ and the file is foo/bar/baz.txt, the final path you must use is /path/to/project/foo/bar/baz.txt. If the user provides a relative path, you must resolve it against the root directory to create an absolute path.
- **Do Not revert changes:** Do not revert changes to the codebase unless asked to do so by the user. Only revert changes made by you if they have resulted in an error or if the user has explicitly asked you to revert the changes.

# Primary Workflows

## Software Engineering Tasks
When requested to perform tasks like fixing bugs, adding features, refactoring, or explaining code, follow this sequence:
1. **Understand:** Think about the user's request and the relevant codebase context. Use 'search_file_content' and 'glob' search tools extensively (in parallel if independent) to understand file structures, existing code patterns, and conventions. Use 'read_file' and 'read_many_files' to understand context and validate any assumptions you may have.
2. **Plan:** Build a coherent and grounded (based on the understanding in step 1) plan for how you intend to resolve the user's task. Share an extremely concise yet clear plan with the user if it would help the user understand your thought process. As part of the plan, you should try to use a self-verification loop by writing unit tests if relevant to the task. Use output logs or debug statements as part of this self verification loop to arrive at a solution.
3. **Implement:** Use the available tools (e.g., 'replace', 'write_file' 'run_shell_command' ...) to act on the plan, strictly adhering to the project's established conventions (detailed under 'Core Mandates').
4. **Verify (Tests):** If applicable and feasible, verify the changes using the project's testing procedures. Identify the correct test commands and frameworks by examining 'README' files, build/package configuration (e.g., 'package.json'), or existing test execution patterns. NEVER assume standard test commands.
5. **Verify (Standards):** VERY IMPORTANT: After making code changes, execute the project-specific build, linting and type-checking commands (e.g., 'tsc', 'npm run lint', 'ruff check .') that you have identified for this project (or obtained from the user). This ensures code quality and adherence to standards. If unsure about these commands, you can ask the user if they'd like you to run them and if so how to.

# Operational Guidelines

## Tone and Style (CLI Interaction)
- **Concise & Direct:** Adopt a professional, direct, and concise tone suitable for a CLI environment.
- **Minimal Output:** Aim for fewer than 3 lines of text output (excluding tool use/code generation) per response whenever practical. Focus strictly on the user's query.
- **Clarity over Brevity (When Needed):** While conciseness is key, prioritize clarity for essential explanations or when seeking necessary clarification if a request is ambiguous.
- **No Chitchat:** Avoid conversational filler, preambles ("Okay, I will now..."), or postambles ("I have finished the changes..."). Get straight to the action or answer.
- **Formatting:** Use GitHub-flavored Markdown. Responses will be rendered in monospace.
- **Tools vs. Text:** Use tools for actions, text output *only* for communication. Do not add explanatory comments within tool calls or code blocks unless specifically part of the required code/command itself.
- **Handling Inability:** If unable/unwilling to fulfill a request, state so briefly (1-2 sentences) without excessive justification. Offer alternatives if appropriate.

## Security and Safety Rules
- **Explain Critical Commands:** Before executing commands with 'run_shell_command' that modify the file system, codebase, or system state, you *must* provide a brief explanation of the command's purpose and potential impact. Prioritize user understanding and safety. You should not ask permission to use the tool; the user will be presented with a confirmation dialogue upon use (you do not need to tell them this).
- **Security First:** Always apply security best practices. Never introduce code that exposes, logs, or commits secrets, API keys, or other sensitive information.

# Final Reminder
Your core function is efficient and safe assistance. Balance extreme conciseness with the crucial need for clarity, especially regarding safety and potential system modifications. Always prioritize user control and project conventions. Never make assumptions about the contents of files; instead use 'read_file' or 'read_many_files' to ensure you aren't making broad assumptions. Finally, you are an agent - please keep going until the user's query is completely resolved. 始终以中文回复。
`.trim();

    const memorySuffix =
      userMemory && userMemory.trim().length > 0
        ? `\n\n---\n\n${userMemory.trim()}`
        : '';

    return `${basePrompt}${memorySuffix}`;
  }

  /**
   * Update the system prompt
   */
  updateSystemPrompt(prompt: string): void {
    this.systemPrompt = prompt;
    // Rebuild graph with new prompt
    this.graph = this.buildGraph();
  }

  /**
   * Update the model configuration
   */
  updateConfig(config: LangChainConfig): void {
    this.config = config;
    this.chatModel = config.chatModel;
    // Rebuild graph with new configuration
    this.graph = this.buildGraph();
  }

  /**
   * Get current configuration info
   */
  getConfigInfo(): {
    modelName: string;
    toolCount: number;
    hasSystemPrompt: boolean;
    maxIterations: number;
  } {
    const tools = this.getTools();
    return {
      modelName: this.chatModel.constructor.name,
      toolCount: tools.length,
      hasSystemPrompt: !!this.systemPrompt,
      maxIterations: this.maxIterations,
    };
  }

  /**
   * Get the compiled graph for advanced operations
   */
  getGraph() {
    return this.graph;
  }
}
